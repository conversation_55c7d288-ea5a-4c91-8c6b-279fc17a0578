<template>
  <div class="complete-quotation-container">
    <div class="card">
      <div><i class="pi pi-arrow-left" style="font-size: 2rem; cursor: pointer;" @click="goBack"></i></div>
      <Divider/>

      <div class="formgrid grid">
        <div v-for="field in schema.quotationInfo"
        :key="field.key"
        class="field col-12 md:col-6 lg:col-4">
          <label :for="field.key" class="block mb-2">
            {{ field.label }}
            <span v-if="field.required" class="text-danger">*</span>
          </label>
          
          <InputText 
            v-if="field.type === 'text' || 
            field.type === 'textarea'"
            :id="field.key"
            v-model="formData[field.key as keyof CotizacionCompletaInterface]"
            :type="field.type"
            :placeholder="field.placeholder"
            class="w-full"
            v-tooltip="field.tooltip" />

          <Dropdown
            v-if="
              field.type === 'select' && field.options === 'plasticTypes'
            "
            :id="field.key"
            v-model="formData.packagingPlastic"
            :options="plasticTypes || []"
            optionLabel="ptPlastic"
            :placeholder="field.placeholder"
            class="w-full"
            v-tooltip="field.tooltip"
            filter />

          <div v-if="field.type === 'radio'" class="flex flex-wrap gap-3">
            <div
              v-for="option in field.options"
              :key="typeof option === 'object' ? option.value : option"
              class="flex align-items-center">
              <RadioButton
                :id="`${field.key}-${
                  typeof option === 'object' ? option.value : option
                }`"
                v-model="formData[field.key as keyof CotizacionCompletaInterface]"
                :value="typeof option === 'object' ? option.value : option"
                :name="`${field.key}`" 
                :v-tooltip="field.tooltip"
                />
              

              <label
                :for="`${field.key}-${
                  typeof option === 'object' ? option.value : option
                }`"
                class="ml-2"
                >{{ typeof option === "object" ? option.label : option }}
                
              </label>
            </div>
          </div>
        </div>
        <div>
          <Button
            label="Submit"
            icon="pi pi-check"
            class="p-button-success"
            @click="submitForm"
           />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { quotationFormSchema } from '@/composables/quotationFormSchema';
import type { CotizacionCompletaInterface } from '@/interfaces/CotizacionCompletaInterface';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { getPlasticType } from '@/service/PlasticTypeService';
import type { PlasticType } from '@/interfaces/PlasticTypeInterface';
import { createSupplierResponseItem } from '@/service/QuotationService';

onMounted(async () => {
  try {
    await fetchPlasticTypes();
  } catch (err) {
    console.error('Error loading plastic types:', err);
  }
});

const schema = quotationFormSchema;
const router = useRouter();

const goBack = () => {
  router.push('/quotations');
};


const formData = ref<CotizacionCompletaInterface>({
  materialPrice: null,
  accessoriesPrice:  null,
  labourCost: null,
  oekotexCetificate: null,
  totalProductPrice: null,
  packagingPlastic: 0,
  packagingFSC: null,
  packagingFobUsdPrice: null,
  sensorMaticCost: null,
  supplierInnerQuantity: null,
  innerDescription: '',
  cbmInnerM3: null,
  supplierMasterQuantity: null,
  masterDescription: '',
  cbmMasterM3: null,
  masterWeightsKg: null,
  masterInnerFscCertificate: null,
  moq: null,
  moqConditionId: null,
  shipmentPort: '',
  finalFobPrice: null,
  finalFobPriceFsc: null,
  productionLeadTimeFirstOrder: null,
  productionLeadTimeRepeatOrder: null,
  remarksHandlingCharge: '',
});


const plasticTypes = ref<PlasticType[] | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);
const errorDetails = ref<any>(null);

const fetchPlasticTypes = async () => {
  try {
    loading.value = true;
    error.value = null;
    errorDetails.value = null;
    plasticTypes.value = null;

    const data = await getPlasticType();
    console.log('Datos recibidos:', data);

    if (typeof data === 'string' && data.includes('<!doctype html>')) {
      throw new Error('El servidor devolvió HTML en lugar de JSON');
    }

    if (Array.isArray(data)) {
      plasticTypes.value = data;
    } else if (data && typeof data === 'object') {
      plasticTypes.value = [data];
    } else {
      throw new Error('Formato de datos inesperado');
    }
  } catch (err: any) {
    console.error('Error:', err);
    error.value = 'Error al cargar los tipos de plástico';
    errorDetails.value = err.response?.data || err.message;
  } finally {
    loading.value = false;
  }
};

async function submitForm() {
  try {
    const response = await createSupplierResponseItem(formData.value);
    console.log("Form submitted successfully", response);
  } catch (err) {
    console.error('Error:', err);
  }
}


</script>

<style scoped>

.text-danger {
  color: var(--red-500);
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}
.complete-quotation-container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

</style>