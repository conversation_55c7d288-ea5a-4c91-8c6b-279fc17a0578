<template>
    <div class="detail">
        <QuotationDetail />
    </div>

    <div class="form">
        <QuotationForm />
    </div>

</template>

<script lang="ts" setup>
import QuotationDetail from './QuotationDetail.vue';
import QuotationForm from '@/components/forms/QuotationForm.vue';

</script>
<style scoped>
.form {
    padding-top: 14rem;
}

.detail {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}

</style>