// src/services/supplierResponseItemService.ts
import type { CotizacionCompletaInterface } from '@/interfaces/CotizacionCompletaInterface';
x
// const BASE_URL = 'http://localhost:8080';
const API_URL_QUOTATION_MANAGEMENT = import.meta.env.VITE_API_URL_QUOTATION_MANAGEMENT;
const API_URL = `${API_URL_QUOTATION_MANAGEMENT}/api/supplier-response-items`;


export interface SupplierResponseItemDTO {
  id?: number;
  // Add other fields from your DTO here
  [key: string]: any;
}

export interface HeaderSupplierResponseDTO {
  // Define the structure of your header DTO here
  [key: string]: any;
}

export interface BulkResponseDTO {
  // Define the structure of your bulk response DTO here
  success: HeaderSupplierResponseDTO[];
  failed: {
    header: HeaderSupplierResponseDTO;
    error: string;
  }[];
}

/**
 * Fetches all supplier response items
 * @returns Promise with list of SupplierResponseItemDTO
 */
export async function getAllSupplierResponseItems(): Promise<SupplierResponseItemDTO[]> {
  try {
    const response = await fetch(API_URL, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error fetching supplier response items');
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getAllSupplierResponseItems:', error);
    throw error;
  }
}

/**
 * Fetches a single supplier response item by ID
 * @param id - Item ID
 * @returns Promise with SupplierResponseItemDTO
 */
export async function getSupplierResponseItemById(id: number): Promise<SupplierResponseItemDTO> {
  try {
    const response = await fetch(`${API_URL}/${id}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Supplier response item not found');
      }
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error fetching supplier response item');
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in getSupplierResponseItemById for ID ${id}:`, error);
    throw error;
  }
}

/**
 * Creates a new supplier response item
 * @param item - Item data to create
 * @returns Promise with created SupplierResponseItemDTO
 */
export async function createSupplierResponseItem(item: CotizacionCompletaInterface): Promise<SupplierResponseItemDTO>{
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(item)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error creating supplier response item');
    }

    return await response.json();
  } catch (error) {
    console.error('Error in createSupplierResponseItem:', error);
    throw error;
  }
}

/**
 * Creates bulk response headers
 * @param headers - List of headers to create
 * @returns Promise with BulkResponseDTO
 */
export async function createBulkResponseHeaders(headers: HeaderSupplierResponseDTO[]): Promise<BulkResponseDTO> {
  try {
    const response = await fetch(`${API_URL}/header`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(headers)
    });

    if (!response.ok && response.status !== 207) { // 207 is Multi-Status
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error creating bulk response headers');
    }

    return await response.json();
  } catch (error) {
    console.error('Error in createBulkResponseHeaders:', error);
    throw error;
  }
}

/**
 * Deletes a supplier response item
 * @param id - Item ID to delete
 * @returns Promise that resolves when deletion is successful
 */
export async function deleteSupplierResponseItem(id: number): Promise<void> {
  try {
    const response = await fetch(`${API_URL}/${id}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Supplier response item not found');
      }
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error deleting supplier response item');
    }
  } catch (error) {
    console.error(`Error in deleteSupplierResponseItem for ID ${id}:`, error);
    throw error;
  }
}