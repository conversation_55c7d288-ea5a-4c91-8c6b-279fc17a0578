import axios from 'axios';

const API_URL_QUOTATION = import.meta.env.VITE_API_URL_QUOTATION_MANAGEMENT;

export function SupplierResponseService() {
    
    const getSupplierResponse = async () => {
        try {
            const response = await axios.get(`${API_URL_QUOTATION}/api/supplier-response-items`);
            console.log('SupplierResponse', response.data)
            return response.data;
        } catch (error) {
            console.error('Error al obtener los tipos de proveedores:', error);
            throw error;
        }
        
    };

    const createSupplierResponse = async (jsonData: object) => {
        try {
            console.log("Sending data to backend:", jsonData);
            return axios.post(`${API_URL_QUOTATION}/api/supplier-response-items`, jsonData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error("Error sending data to backend:", error);
        }
    
    };

    return {
        getSupplierResponse,
        createSupplierResponse
    };
};