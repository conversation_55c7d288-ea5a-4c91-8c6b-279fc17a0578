import { createRouter, createWebHistory } from 'vue-router';
import Home from '@/views/Home.vue';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      name: "Home",
      component: Home,
    },
    {
      path: "/company-profile",
      name: "CompanyProfile",
      component: () => import("@/views/CompanyProfile/CompanyProfile.vue"),
    },
    {
      path: "/factories",
      name: "Factories",
      component: () => import("@/views/Factories/Factories.vue"),
    },
    {
      path: "/quotations",
      name: "Quotations",
      component: () => import("@/views/Quotations/Quotations.vue"),
    },
    {
      path: "/quotations/quotationdetail",
      name: "QuotationDetail",
      component: () => import("@/views/Quotations/QuotationDetail.vue"),
    },
    {
      path: "/supplierquotation",
      name: "SupplierQuotation",
      component: () => import("@/views/Quotations/SupplierQuotation.vue"),
    },
    {
      path: "/test",
      name: "Test",
      component: () => import("/test/EndpointTests.vue"),
    },
  ],
});

export default router;