<template>
  <div
    class="navigation-card"
    :class="{ 'disabled': disabled }"
    @click="navigateToRoute"
  >
    <div class="card-icon">
      <i :class="icon"></i>
    </div>
    <div class="card-content">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
    </div>
    <div class="card-status" v-if="disabled">
      <span class="status-badge">
        <i class="pi pi-lock"></i> Locked
      </span>
    </div>
    <div class="card-arrow" v-else>
      <i class="pi pi-arrow-right"></i>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  route: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();

const navigateToRoute = () => {
  if (!props.disabled) {
    router.push(props.route);
  }
};
</script>

<style scoped>
.navigation-card {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  min-height: 120px;
}

.navigation-card:hover:not(.disabled) {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.navigation-card.disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #f8f9fa;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: var(--primary-color-lighter, #f0f7ff);
  margin-right: 1.5rem;
  flex-shrink: 0;
}

.card-icon i {
  font-size: 2rem;
  color: var(--primary-color, #2196F3);
}

.card-content {
  flex: 1;
}

.card-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: var(--text-color, #212529);
  font-weight: 600;
}

.card-content p {
  margin: 0;
  color: var(--text-color-secondary, #6c757d);
  font-size: 1rem;
  line-height: 1.5;
}

.card-arrow {
  margin-left: 1rem;
}

.card-arrow i {
  font-size: 1.5rem;
  color: var(--primary-color, #2196F3);
}

.card-status {
  margin-left: 1rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  background-color: #f8f9fa;
  color: #6c757d;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-badge i {
  margin-right: 0.25rem;
  font-size: 0.75rem;
}
</style>
