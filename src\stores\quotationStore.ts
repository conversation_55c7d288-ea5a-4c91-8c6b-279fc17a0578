import { defineStore } from "pinia";
import type { Cotizacion } from "@/interfaces/CotizacionInterface";


export const useCotizacionStore = defineStore('cotizacion', {
  state: () => ({
    cotizacionSeleccionada: null as Cotizacion | null
  }),
  actions: {
    setCotizacion(cotizacion: Cotizacion) {
      this.cotizacionSeleccionada = cotizacion;
    },
    limpiar() {
      this.cotizacionSeleccionada = null;
    }
  }
});