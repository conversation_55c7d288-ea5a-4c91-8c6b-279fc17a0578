<template>
    <div v-for="field in schema.certificateSchema ">
        <div>
            <label>
                FCC Certificate
            </label>
        </div>
        <div>
            <RadioButton

            />
        </div>

    </div>
</template>

<script lang="ts">
import { RadioButton } from 'primevue';
import { certificates } from '@/composables/supplierFormSchema';
const schema = certificates

export default {
    name: 'Certificates',
};

</script>