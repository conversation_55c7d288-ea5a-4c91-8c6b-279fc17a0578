import axios from 'axios';

const API_URL_QUOTATION = import.meta.env.VITE_API_URL_QUOTATION_MANAGMENT;

export const getPlasticType = async () => {
  try {
    console.log('URL de la API:', `${API_URL_QUOTATION}/api/plastic`);
    const response = await axios.get(`${API_URL_QUOTATION}/api/plastic`, {
      headers: {
        'Accept': 'application/json'
      }
    });
    console.log('Respuesta completa:', response);
    return response.data;
  } catch (error) {
    console.error('Error en getPlasticType:', error);
    throw error;
  }
};