// Define los campos por sección para el formulario de perfil de empresa
export const supplierFormSchema = {
  companyInfo: [
    { key: "companyName", label: "Company Name", type: "text", required: true, placeholder: "Enter company name", tooltip: "Official registered name of your company" },
    { key: "companyOwnerName", label: "Owner Name", type: "text", required: true, placeholder: "Enter owner name", tooltip: "Full name of the company owner" },
    { key: "companyOwnerPhone", label: "Owner Phone", type: "tel", required: true, placeholder: "+****************", tooltip: "Contact phone number of the company owner" },
    { key: "companyOwnerEmail", label: "Owner Email", type: "email", required: true, placeholder: "<EMAIL>", tooltip: "Email address of the company owner" },
    { key: "socialCreditCode", label: "Social Credit Code/CIN", type: "text", required: true, placeholder: "Enter social credit code", tooltip: "Your company's social credit code or CIN" },
    { key: "exportLicenseCode", label: "Export License Code", type: "text", required: true, placeholder: "Enter export license code", tooltip: "Your company's export license code" },
    { key: "address", label: "Address", type: "text", required: true, placeholder: "Enter company address", tooltip: "Official registered address of your company" },
    { key: "country_id", label: "Country", type: "select", options: "countries", required: true, placeholder: "Select a country", tooltip: "Country where your company is registered" },
    { key: "province", label: "Province/County", type: "text", required: true, placeholder: "Enter province or county", tooltip: "Province or county where your company is located" },
    { key: "city", label: "City", type: "text", required: true, placeholder: "Enter city", tooltip: "City where your company is located" },
    { key: "state", label: "State", type: "text", required: true, placeholder: "Enter state", tooltip: "State where your company is located" },
    { key: "contactName", label: "Contact Name", type: "text", required: true, placeholder: "Enter contact person name", tooltip: "Name of the primary contact person" },
    { key: "phone", label: "Phone Number", type: "tel", required: true, placeholder: "+****************", tooltip: "Main contact phone number" },
    { key: "email", label: "Email Address", type: "email", required: true, placeholder: "<EMAIL>", tooltip: "Main contact email address" },
    { key: "currencyId", label: "Currency", type: "select", options: "currencies", required: true, placeholder: "Select a currency", tooltip: "Preferred currency for transactions" },
    { key: "supplierType", label: "Supplier Type", type: "select", options: "supplierTypes", required: true, placeholder: "Select a supplier type", tooltip: "Type of supplier your company represents" },
    { key: "supplierLvl3", label: "Supplier Category", type: "select", options: "supplierCategories", required: true, placeholder: "Select a supplier category", tooltip: "Category of supplier your company falls under" },
    { key: "annualFacturation", label: "Annual Facturation", type: "text", required: true, placeholder: "Enter annual facturation", tooltip: "Estimated annual facturation for your company" },
  ],
  bankInfo: [
    { key: "bankName", label: "Bank Name", type: "text", required: true, placeholder: "Enter bank name", tooltip: "Name of your company's bank" },
    { key: "bankAddress", label: "Bank Address", type: "text", required: true, placeholder: "Enter bank address", tooltip: "Address of your bank" },
    { key: "swiftCode", label: "Swift Code", type: "text", required: true, placeholder: "Enter SWIFT/BIC code", tooltip: "Bank's SWIFT or BIC code for international transfers" },
    { key: "ibanNumber", label: "IBAN Number", type: "text", required: false, placeholder: "Enter IBAN number (if applicable)", tooltip: "International Bank Account Number" },
    { key: "accountNumber", label: "Account Number", type: "text", required: true, placeholder: "Enter account number", tooltip: "Your bank account number" },
    { key: "beneficiaryName", label: "Beneficiary Name", type: "text", required: true, placeholder: "Enter beneficiary name", tooltip: "Name of the account holder" },
    { key: "beneficiaryAddress", label: "Beneficiary Address", type: "text", required: true, placeholder: "Enter beneficiary address", tooltip: "Address of the account holder" },
  ],
  factoryInfo: [
    { key: "factories", label: "Factories", type: "factories", required: true, tooltip: "Information about your manufacturing facilities" },
  ]
};

// Esquema para cada fábrica
export const factorySchema = [
  { key: "factorName", label: "Factory Name", type: "text", required: true, placeholder: "Enter factory name", tooltip: "Name of the manufacturing facility" },
  { key: "factoryAddress", label: "Factory Address", type: "text", required: true, placeholder: "Enter factory address", tooltip: "Physical address of the factory" },
  { key: "country_id", label: "Country", type: "select", options: "countries", required: true, placeholder: "Select a country", tooltip: "Country where the factory is located" },
  { key: "factoryProvince", label: "Province/County", type: "text", required: true, placeholder: "Enter province or county", tooltip: "Province or county where the factory is located" },
  { key: "factoryCity", label: "City", type: "text", required: true, placeholder: "Enter city", tooltip: "City where the factory is located" },
  { key: "factoryState", label: "State", type: "text", required: true, placeholder: "Enter state", tooltip: "State where the factory is located" },
  { key: "zipCode", label: "ZIP Code", type: "text", required: true, placeholder: "Enter ZIP code", tooltip: "Postal code of the factory location" },
  { key: "factoryPhone", label: "Phone", type: "tel", required: true, placeholder: "+****************", tooltip: "Contact phone number for the factory" },
  { key: "exportLicenceCode", label: "Export License Code", type: "text", required: false, placeholder: "Enter export license code (if applicable)", tooltip: "Factory's export license code" },
  { key: "mainProduct", label: "Main Product", type: "text", required: false, placeholder: "Enter main product", tooltip: "Primary product manufactured at this facility" },
  { key: "finishProduct", label: "Finished Product", type: "radio", options: [{label: "Yes", value: "yes"}, {label: "No", value: "no"}], required: true, tooltip: "Does this factory deliver the finished product?" },
];

export const certificatesSchema = 
[
    { key: "fccCertificate", label: "FCC Certifiacate ", type: "radio", options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}, {label: "No aplicable", value:"noAplicable"}], required: false, tooltip: "Do you have FCC certificate?" },
    { key: "oekotexCertificate", label: " Oekotex Certifiacate ", type: "radio", options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}, {label: "No aplicable", value:"noAplicable"}], required: false, tooltip: "Do you have Oekotex certificate?" },
    { key: "bsciCertificate", label: "BSCI Certifiacate", type: "radio", options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}, {label: "No aplicable", value:"noAplicable"}], required: false, tooltip: "Do you have BSIC certificate?" },
    { key: "iso9001Certificate", label: "ISO9001 Certifiacate", type: "radio", options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}, {label: "No aplicable", value:"noAplicable"}], required: false, tooltip: "Do you have ISO9001 certificate?" },
    { key: "otherCertificate", label: "Others Certifiacates", type: "radio", options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}, {label: "No aplicable", value:"noAplicable"}], required: false, tooltip: "Do you have any other certificate?" },

]
