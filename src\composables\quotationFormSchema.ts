export const quotationFormSchema = {
    quotationInfo: [
            { key: "materialPrice", label: "Material Price", type: "text", required: true, placeholder: "Enter the material price", tooltip: "Please enter the material price" },
            { key: "accessoriesPrice", label: "Accessories Price", type: "text", required: true, placeholder: "Enter the accessories price", tooltip: "Please enter the accessories price" },
            { key: "labourCost", label: "Labour Cost", type: "text", required: true, placeholder: "Enter the labour cost", tooltip: "Please enter the labour cost" },
            { key: "oekotexCetificate", label: "Oekotex Certificate", type: "radio", options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}],required: true, placeholder: "Do you have an Oekotex certificate?", tooltip: "Please confirm if you have an Oekotex certificate" },
            // totalProductPrice se hace automaticamente
            // packagingSpecification lo rellena el comprador
            { key: "packagingPlastic", label: "Packaging (Plastic)", type: "select", options: "plasticTypes", required: true, placeholder: "Especify the type of plastic used for packaging", tooltip: "the packaging contains any plastic, please inform us witch type of plastic is, for example, PET, PVC, ABS..." },
            { key: "packagingFSC", label: "Certified Packaging (FSC)", type: "radio",  options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}], required: true, placeholder: "Do you have a FSC certificate?", tooltip: "Please confirm if you use cardboard - paper certificated with FSC" },
            { key: "packagingFobUsdPrice", label: "Packaging (FOB USD Price)", type: "text", required: true, placeholder: "Enter the packaging Fob Usd Price", tooltip: "Please enter the packaging Fob Usd Price" },
            // useSensorMatic lo rellena el comprador
            { key: "sensorMaticFobUsdPrice", label: "Sensor Matic FOB USD Price", type: "text", required: true, placeholder: "Enter the sensor matic fob usd price", tooltip: "Please enter the sensor matic fob usd price" },
    
            { key: "supplierInnerQuantity", label: "Supplier Inner Quantity", type: "text", required: true, placeholder: "Enter the supplier inner quantity", tooltip: "Please enter the supplier inner quantity" },
            { key: "innerDescription", label: "Inner Description", type: "text", required: true, placeholder: "Enter the inner description", tooltip: "Please describe the inner product" },
            { key: "cbmInnerM3", label: "CBM Inner (M3)", type: "text", required: true, placeholder: "Enter the CBM Inner (M3)", tooltip: "Please enter the CBM Inner (M3)" },
            { key: "supplierMasterQuantity", label: "Supplier Master Quantity", type: "text", required: true, placeholder: "Enter the supplier master quantity", tooltip: "Please enter the supplier master quantity" },
            { key: "masterDescription", label: "Master Description", type: "text", required: true, placeholder: "Enter the master description", tooltip: "Please describe the master product" },
            { key: "cbmMasterM3", label: "CBM Master (M3)", type: "text", required: true, placeholder: "Enter the CBM Master (M3)", tooltip: "Please enter the CBM Master (M3)" },
            { key: "masterWeightsKg", label: "Master Weights (Kg)", type: "text", required: true, placeholder: "Enter the master weights (Kg)", tooltip: "Please enter the master weights (Kg)" },
            { key: "masterInnerFscCertificate", label: "Master Inner FSC Certificate", type: "radio", options: [{label: "Yes", value: "Yes"}, {label: "No", value: "no"}], required: true, placeholder: "confirm if you use cardboard certificated with FSC", tooltip: "Please confirm if you use cardboard certificated with FSC" },
            { key: "moq", label: "MOQ", type: "text", required: true, placeholder: "Enter the MOQ", tooltip: "Please enter the MOQ" },
            { key: "moqConditionId", label: "MOQ Condition Id", type: "text", required: true, placeholder: "Enter the MOQ condition id", tooltip: "Please enter the MOQ condition id" },
            { key: "shipmentPort", label: "Shipment Port", type: "text", required: true, placeholder: "Enter the shipment port", tooltip: "Please enter the shipment port" },
            { key: "finalFobPrice", label: "Final FOB Price", type: "text", required: true, placeholder: "Enter the final fob price", tooltip: "Please enter the final fob price" },
            { key: "finalFobPriceFsc", label: "Final FOB Price with FSC / Oekotex", type: "text", required: true, placeholder: "Enter the final fob price with FSC / Oekotex", tooltip: "Please enter the final fob price with FSC / Oekotex" },
            { key: "productionLeadTimeFirstOrder", label: "Production Lead Time for First Order", type: "text", required: true, placeholder: "Enter the production lead time for first order", tooltip: "Please enter the production lead time for first order" },
            { key: "productionLeadTimeRepeatOrder", label: "Production Lead Time for Repeat Order", type: "text", required: true, placeholder: "Enter the production lead time for repeat order", tooltip: "Please enter the production lead time for repeat order" },
            { key: "remarksHandlingCharge", label: "Remarks / Handling Charge", type: "text", required: true, placeholder: "Enter the remarks / handling charge", tooltip: "Please enter the remarks / handling charge" },
        ],
};
