import axios from 'axios';

const API_URL = 'http://localhost:8090/api/suppliers/status';

// Obtener el estado del perfil del proveedor
export const getSupplierProfileStatus = async () => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching supplier profile status:', error);
    return { hasActiveProfile: false };
  }
};
