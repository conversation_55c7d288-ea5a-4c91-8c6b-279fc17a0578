import axios from "axios";

const API_URL = 'http://localhost:8090/api/suppliers';
const API_URL2 = 'http://localhost:8090/api/nivel3';
const API_URL3 = 'http://localhost:8090/api/typesupplier';

export const createSupplier = (jsonData: object) => {
  console.log("Sending data to backend:", jsonData);
  try {
    console.log("Sending data to backend:", jsonData);
    return axios.post(API_URL, jsonData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error("Error sending data to backend:", error);
  }

};

export const getSupplierNivel3 = async () => {
  try {
    const response = await axios.get(API_URL2);
    console.log('Supplier Nivel3:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching supplier group:', error);
    throw error;
  }
};

export const getSupplierType = async () => {
  try {
    const response = await axios.get(API_URL3);
    console.log('Supplier tipo:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching supplier status:', error);
    throw error;
  }
};