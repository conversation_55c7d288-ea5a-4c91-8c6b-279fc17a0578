<template>
  <div class="company-profile-container">
    <Toast position="top-right" />
    <div class="card">
      <div>
        <i
          class="pi pi-arrow-left"
          style="font-size: 2rem; cursor: pointer"
          @click="goBack"></i>
      </div>

      <h1 class="text-center mb-4">Company Profile</h1>
      <p class="text-center text-500 mb-5">
        Please complete all required information to register your company
      </p>

      <div class="stepper-container mb-5">
        <Steps :model="steps" :readonly="true" :activeStep="activeStep" />
      </div>

      <div class="step-content p-4">
        <!-- Step 1: Company Information -->
        <div v-if="activeStep === 0" class="step-panel">
          <h2 class="step-title">Company Information</h2>
          <p class="step-description">
            Please provide your company's basic information
          </p>
          <Divider />

          <div class="formgrid grid">
            <div
              v-for="field in schema.companyInfo"
              :key="field.key"
              class="field col-12 md:col-6 lg:col-4">
              <label :for="field.key" class="block mb-2">
                {{ field.label }}
                <span v-if="field.required" class="text-danger">*</span>
              </label>

              <!-- Input text, email, tel -->
              <InputText
                v-if="
                  field.type === 'text' ||
                  field.type === 'email' ||
                  field.type === 'tel'
                "
                :id="field.key"
                v-model="formData[field.key as keyof SupplierInformationForm]"
                :type="field.type"
                :placeholder="field.placeholder"
                :class="{ 'p-invalid': validationErrors[field.key] }"
                class="w-full"
                v-tooltip="field.tooltip" />

              <!-- Select de países -->
              <Dropdown
                v-else-if="
                  field.type === 'select' && field.options === 'countries'
                "
                :id="field.key"
                v-model="formData.country_id"
                :options="countries"
                optionLabel="countryName"
                :placeholder="field.placeholder"
                :class="{ 'p-invalid': validationErrors[field.key] }"
                class="w-full"
                v-tooltip="field.tooltip"
                filter />

              <!-- Select de monedas -->
              <Dropdown
                v-else-if="
                  field.type === 'select' && field.options === 'currencies'
                "
                :id="field.key"
                v-model="formData.currencyId"
                :options="currency"
                optionLabel="currencyName"
                :placeholder="field.placeholder"
                :class="{ 'p-invalid': validationErrors[field.key] }"
                class="w-full"
                v-tooltip="field.tooltip"
                filter />

              <!-- Select de tipos de proveedor -->
              <Dropdown
                v-else-if="
                  field.type === 'select' && field.options === 'supplierTypes'
                "
                :id="field.key"
                v-model="formData.status_id"
                :options="statusProveedor"
                optionLabel="typeName"
                :placeholder="field.placeholder"
                :class="{ 'p-invalid': validationErrors['status_id'] }"
                class="w-full"
                v-tooltip="field.tooltip"
                filter />

              <!-- Select de categorías de proveedor -->
              <Dropdown
                v-else-if="
                  field.type === 'select' &&
                  field.options === 'supplierCategories'
                "
                :id="field.key"
                v-model="formData.nivel3"
                :options="supplierLvl3"
                optionLabel="descripcion"
                :placeholder="field.placeholder"
                :class="{ 'p-invalid': validationErrors['nivel3'] }"
                class="w-full"
                v-tooltip="field.tooltip"
                filter />

              <small
                v-if="
                  validationErrors[field.key] ||
                  (field.key === 'supplierType' &&
                    validationErrors['status_id']) ||
                  (field.key === 'supplierLvl3' && validationErrors['nivel3'])
                "
                class="p-error block mt-1"
                >{{
                  validationErrors[field.key] ||
                  (field.key === "supplierType"
                    ? validationErrors["status_id"]
                    : "") ||
                  (field.key === "supplierLvl3"
                    ? validationErrors["nivel3"]
                    : "")
                }}</small
              >
            </div>
          </div>

          <div class="flex justify-content-end mt-4">
            <Button
              label="Next"
              icon="pi pi-arrow-right"
              iconPos="right"
              @click="nextStep" />
          </div>
        </div>

        <!-- Step 2: Bank Information -->
        <div v-if="activeStep === 1" class="step-panel">
          <h2 class="step-title">Bank Information</h2>
          <p class="step-description">
            Please provide your company's banking details
          </p>
          <Divider />

          <div class="formgrid grid">
            <div
              v-for="field in schema.bankInfo"
              :key="field.key"
              class="field col-12 md:col-6">
              <label :for="field.key" class="block mb-2">
                {{ field.label }}
                <span v-if="field.required" class="text-danger">*</span>
              </label>

              <InputText
                :id="field.key"
                v-model="formData[field.key as keyof SupplierInformationForm]"
                :type="field.type"
                :placeholder="field.placeholder"
                :class="{ 'p-invalid': validationErrors[field.key] }"
                class="w-full"
                v-tooltip="field.tooltip" />

              <small
                v-if="validationErrors[field.key]"
                class="p-error block mt-1"
                >{{ validationErrors[field.key] }}</small
              >
            </div>
          </div>

          <div class="flex justify-content-between mt-4">
            <Button
              label="Previous"
              icon="pi pi-arrow-left"
              class="p-button-secondary"
              @click="prevStep" />
            <Button
              label="Next"
              icon="pi pi-arrow-right"
              iconPos="right"
              @click="nextStep" />
          </div>
        </div>

        <!-- Step 3: Factory Information -->
        <div v-if="activeStep === 2" class="step-panel">
          <h2 class="step-title">Factory Information</h2>
          <p class="step-description">
            Please provide information about your manufacturing facilities
          </p>
          <Divider />

          <div
            v-for="(factory, factoryIndex) in formData.factories"
            :key="factoryIndex"
            class="factory-card mb-4 p-3 border-round">
            <div class="flex justify-content-between align-items-center mb-3">
              <h3 class="factory-title m-0">Factory #{{ factoryIndex + 1 }}</h3>
              <Button
                v-if="factoryIndex > 0"
                icon="pi pi-trash"
                class="p-button-danger p-button-sm"
                @click="removeFactory(factoryIndex)" />
            </div>

            <div class="formgrid grid">
              <div
                v-for="field in factorySchema"
                :key="field.key"
                class="field col-12 md:col-6 lg:col-4">
                <label :for="`${field.key}-${factoryIndex}`" class="block mb-2">
                  {{ field.label }}
                  <span v-if="field.required" class="text-danger">*</span>
                </label>

                <!-- Input text, email, tel -->
                <InputText
                  v-if="
                    field.type === 'text' ||
                    field.type === 'email' ||
                    field.type === 'tel'
                  "
                  :id="`${field.key}-${factoryIndex}`"
                  v-model="factory[field.key as keyof typeof factory]"
                  :type="field.type"
                  :placeholder="field.placeholder"
                  :class="{
                    'p-invalid': !!getFactoryValidationError(
                      factoryIndex,
                      field.key
                    ),
                  }"
                  class="w-full"
                  v-tooltip="field.tooltip" />

                <!-- Select de países -->
                <Dropdown
                  v-else-if="
                    field.type === 'select' && field.options === 'countries'
                  "
                  :id="`${field.key}-${factoryIndex}`"
                  v-model="factory.country_id"
                  :options="countries"
                  optionLabel="countryName"
                  :placeholder="field.placeholder"
                  :class="{
                    'p-invalid': !!getFactoryValidationError(
                      factoryIndex,
                      field.key
                    ),
                  }"
                  class="w-full"
                  v-tooltip="field.tooltip"
                  filter />

                <!-- Radio buttons -->
                <div
                  v-else-if="field.type === 'radio'"
                  class="flex flex-wrap gap-3">
                  <div
                    v-for="option in field.options"
                    :key="typeof option === 'object' ? option.value : option"
                    class="flex align-items-center">
                    <RadioButton
                      :id="`${field.key}-${factoryIndex}-${
                        typeof option === 'object' ? option.value : option
                      }`"
                      v-model="factory[field.key as keyof typeof factory]"
                      :value="
                        typeof option === 'object' ? option.value : option
                      "
                      :name="`${field.key}-${factoryIndex}`" />
                    <label
                      :for="`${field.key}-${factoryIndex}-${
                        typeof option === 'object' ? option.value : option
                      }`"
                      class="ml-2">
                      {{ typeof option === "object" ? option.label : option }}
                    </label>
                  </div>
                </div>

                <small
                  v-if="getFactoryValidationError(factoryIndex, field.key)"
                  class="p-error block mt-1">
                  {{ getFactoryValidationError(factoryIndex, field.key) }}
                </small>
              </div>
            </div>
          </div>

          <Button
            label="Add Another Factory"
            icon="pi pi-plus"
            class="p-button-outlined mb-4"
            @click="addFactory" />

          <div class="flex justify-content-between mt-4">
            <Button
              label="Previous"
              icon="pi pi-arrow-left"
              class="p-button-secondary"
              @click="prevStep" />
            <Button
              label="Next"
              icon="pi pi-arrow-right"
              iconPos="right"
              @click="nextStep" />
          </div>
        </div>

        <!-- Cetificados -->
        <div v-if="activeStep === 3">
          <h2 class="step-title">Certificates</h2>
          <p class="step-description">Please upload your Certificates</p>

          <Divider />

          <div
            v-for="field in certificatesSchema"
            :key="field.key"
            class="factory-card mb-4 p-3 border-round column1">
            <label :for="`${field.key}`" class="block mb-2">
              <h6>  
                {{ field.label }}
              </h6>
              <span v-if="field.required" class="text-danger">*</span>
            </label>

            <div v-if="field.type === 'radio'" class="flex flex-wrap gap-3">
              <div
                v-for="option in field.options"
                :key="typeof option === 'object' ? option.value : option"
                class="flex align-items-center">
                <RadioButton
                  :id="`${field.key}-${
                    typeof option === 'object' ? option.value : option
                  }`"
                  v-model="formData[field.key as keyof CertificatesInterface]"
                  :value="typeof option === 'object' ? option.value : option"
                  :name="`${field.key}`" 
                  />
                

                <label
                  :for="`${field.key}-${
                    typeof option === 'object' ? option.value : option
                  }`"
                  class="ml-2"
                  >{{ typeof option === "object" ? option.label : option }}
                  
                </label>
              </div>
              <Divider />
              <!-- Contenedor del botón y la info del archivo -->
              <div class="flex align-items-center gap-3 mt-2 w-full"
              v-if="formData[field.key as keyof SupplierInformationForm] === 'Yes'">
                <!-- Botón de carga -->
                <FileUpload
                  mode="basic"
                  :auto="true"
                  accept="image/*,application/pdf,.xls,.xlsx,application/vnd.ms-excel,.doc,.docx,application/msword,text/plain"
                  :maxFileSize="50000000"
                  :customUpload="true"
                  @select="event => onFileSelect(event, field.key)"
                  chooseLabel="Upload">
                  <template #header="{ chooseCallback }">
                    <Button
                      severity="warn"
                      variant="outlined"
                      label="Importar"
                      raised
                      @click="chooseCallback()"
                      class="bi bi-upload" />
                  </template>
                </FileUpload>

                <!-- Información del archivo subido -->
                  <div v-if="uploadedFiles[field.key]" class="row1">
                    <div
                    class="flex align-items-center px-3 py-2 border-1 border-round border-gray-300 text-sm bg-gray-100"
                    style="max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                    
                    <i class="pi pi-file mr-2"></i>
                    {{ uploadedFiles[field.key]?.name }} -
                    {{ (uploadedFiles[field.key]!.size / 1024).toFixed(2) }} KB
                    
                  </div>
                  <div>  
                    <Button
                    icon="pi pi-trash"
                    severity="danger"
                    text
                    rounded
                    size="small"
                    class="ml-3"
                    @click="uploadedFiles[field.key] = null"
                    aria-label="Eliminar archivo" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex justify-content-between mt-4">
            <Button
              label="Previous"
              icon="pi pi-arrow-left"
              class="p-button-secondary"
              @click="prevStep" />
            <Button
              label="Next"
              icon="pi pi-arrow-right"
              iconPos="right"
              @click="nextStep" />
          </div>
        </div>

        <!-- Step 4: Review & Submit -->
        <div v-if="activeStep === 4" class="step-panel">
          <h2 class="step-title">Review & Submit</h2>
          <p class="step-description">
            Please review your information before submitting
          </p>
          <Divider />

          <div class="review-section mb-4">
            <h3>Company Information</h3>
            <div class="formgrid grid">
              <div
                v-for="field in schema.companyInfo"
                :key="field.key"
                class="field col-12 md:col-6 lg:col-4">
                <div class="review-item">
                  <div class="font-bold">{{ field.label }}</div>
                  <div
                    v-if="
                      field.type === 'select' &&
                      field.options === 'countries' &&
                      formData.country_id
                    ">
                    {{ getCountryName(formData.country_id) }}
                  </div>
                  <div
                    v-else-if="
                    field.type === 'select' &&
                    field.options === 'currencies' &&
                    formData[field.key as keyof SupplierInformationForm]">
                    {{
                      (
                        formData[
                          field.key as keyof SupplierInformationForm
                        ] as any
                      ).currencyName
                    }}
                  </div>
                  <div
                    v-else-if="
                      field.type === 'select' &&
                      field.options === 'supplierTypes' &&
                      formData.status_id
                    ">
                    {{ formData.status_id.typeName }}
                  </div>
                  <div
                    v-else-if="
                      field.type === 'select' &&
                      field.options === 'supplierCategories' &&
                      formData.nivel3
                    ">
                    {{ formData.nivel3.descripcion }}
                  </div>
                  <div v-else>
                    {{
                      formData[field.key as keyof SupplierInformationForm] ||
                      "Not provided"
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="review-section mb-4">
            <h3>Bank Information</h3>
            <div class="formgrid grid">
              <div
                v-for="field in schema.bankInfo"
                :key="field.key"
                class="field col-12 md:col-6">
                <div class="review-item">
                  <div class="font-bold">{{ field.label }}</div>
                  <div>
                    {{
                      formData[field.key as keyof SupplierInformationForm] ||
                      "Not provided"
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="review-section mb-4">
            <h3>Factory Information</h3>
            <div
              v-for="(factory, factoryIndex) in formData.factories"
              :key="factoryIndex"
              class="factory-review mb-3 p-3 border-round">
              <h4>Factory #{{ factoryIndex + 1 }}</h4>
              <div class="formgrid grid">
                <div
                  v-for="field in factorySchema"
                  :key="field.key"
                  class="field col-12 md:col-6 lg:col-4">
                  <div class="review-item">
                    <div class="font-bold">{{ field.label }}</div>
                    <div
                      v-if="
                        field.type === 'select' &&
                        field.options === 'countries' &&
                        factory.country_id
                      ">
                      {{ getCountryName(factory.country_id) }}
                    </div>
                    <div v-else>
                      {{
                        factory[field.key as keyof typeof factory] ||
                        "Not provided"
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-content-between mt-4">
            <Button
              label="Previous"
              icon="pi pi-arrow-left"
              class="p-button-secondary"
              @click="prevStep" />
            <Button
              label="Submit"
              icon="pi pi-check"
              class="p-button-success"
              @click="submitForm"
              :loading="submitting" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import {
  supplierFormSchema,
  factorySchema,
  certificatesSchema,
} from "@/composables/supplierFormSchema";
import { formSteps } from "@/composables/formSteps";
import type { CurrencyDB } from "@/interfaces/CurrencyInterface";
import type { SupplierInformationForm } from "@/interfaces/CompanyProfileInterface";
import type { CountriesDB } from "@/interfaces/PaisesInterface";
import type { FactoryFormType } from "@/interfaces/FactoriesInteface";
import { getCountries } from "@/service/SelecCountries";
import { createSupplier } from "@/service/CompanyProfileService";
import { getCurrency } from "@/service/SelectCurrency";
import { useToast } from "primevue/usetoast";
import { getSupplierNivel3 } from "@/service/CompanyProfileService";
import { getSupplierType } from "@/service/CompanyProfileService";
import type { StatusProveedor } from "@/interfaces/StatusProvedor";
import type { SupplierLvl3Interface } from "@/interfaces/SupplierLvl3Interface";
import type { CertificatesInterface } from "@/interfaces/CertificatesInterface";

const router = useRouter();

// Función para volver a la página anterior
const goBack = () => {
  router.push("/");
};

// Toast para notificaciones
const toast = useToast();

// Paso activo en el stepper
const activeStep = ref(0);
const steps = formSteps;
const submitting = ref(false);

// Datos de países y monedas
const countries = ref<CountriesDB[]>([]);
const currency = ref<CurrencyDB[]>([]);
const statusProveedor = ref<StatusProveedor[]>([]);
const supplierLvl3 = ref<SupplierLvl3Interface[]>([]);
const schema = supplierFormSchema;

// Errores de validación
const validationErrors = reactive<Record<string, string>>({});
const factoryValidationErrors = reactive<
  Record<string, Record<string, string>>
>({});

// aquí se alamcenan los certificados del proveedor
const uploadedFiles = ref<Record<string, File | null>>({});

// Carga de certificados

const onFileSelect = (event: any, key: string) => {
  if (event.files && event.files.length > 0) {
    uploadedFiles.value[key] = event.files[0];
    console.log('El archivo se cargó exitosamente')
  }
};



// Función para inicializar los objetos de selección
function initializeSelectObjects() {
  // Inicializar country_id como objeto vacío si no existe
  if (!formData.value.country_id) {
    formData.value.country_id = {
      country_id: 0,
      countryName: "",
      countryISO: "",
    };
  }

  // Inicializar currencyId como objeto vacío si no existe
  if (!formData.value.currencyId) {
    formData.value.currencyId = {
      currencyId: 0,
      currencyName: "",
      currencyCode: "",
    };
  }

  // Inicializar status_id como objeto vacío si no existe
  if (!formData.value.status_id) {
    formData.value.status_id = {
      typeSupplierId: 0,
      typeName: "",
    };
  }

  // Inicializar nivel3 como objeto vacío si no existe
  if (!formData.value.nivel3) {
    formData.value.nivel3 = {
      id: 0,
      descripcion: "",
    };
  }

  // Inicializar country_id en cada fábrica
  formData.value.factories.forEach((factory) => {
    if (!factory.country_id) {
      factory.country_id = {
        country_id: 0,
        countryName: "",
        countryISO: "",
      };
    }
  });
}

// Cargar datos iniciales
onMounted(async () => {
  try {
    countries.value = await getCountries();
    currency.value = await getCurrency();
    statusProveedor.value = await getSupplierType();
    supplierLvl3.value = await getSupplierNivel3();

    // Inicializar la primera fábrica si no hay ninguna
    if (formData.value.factories.length === 0) {
      addFactory();
    }

    // Inicializar objetos de selección
    initializeSelectObjects();

    console.log("Datos inicializados:", formData.value);
  } catch (err) {
    console.error("Error loading data:", err);
    toast.add({
      severity: "error",
      summary: "Error",
      detail: "Could not load countries or currencies",
      life: 3000,
    });
  }
});

// Datos del formulario
const formData = ref<SupplierInformationForm>({
  companyName: "",
  companyOwnerName: "",
  companyOwnerPhone: "",
  companyOwnerEmail: "",
  socialCreditCode: "",
  exportLicenseCode: "",
  address: "",
  province: "",
  city: "",
  state: "",
  country_id: {
    country_id: 0,
    countryName: "",
    countryISO: "",
  }, // Added missing property
  contactName: "",
  phone: "",
  email: "",

  currencyId: {
    currencyId: 0,
    currencyName: "",
    currencyCode: "",
  },
  status_id: {
    typeSupplierId: 0,
    typeName: "",
  },
  nivel3: {
    id: 0,
    descripcion: "",
  },
  bankName: "",
  bankAddress: "",
  swiftCode: "",
  ibanNumber: "",
  accountNumber: "",
  beneficiaryName: "",
  beneficiaryAddress: "",
  companyProfileStatus: "ACTIVE",
  annualFacturation: 0,

  factories: [],

  certificates: [],
});

// Funciones para navegar entre pasos
function nextStep() {
  if (validateCurrentStep()) {
    if (activeStep.value < steps.length - 1) {
      activeStep.value++;
      window.scrollTo(0, 0);
    }
  }
}

function prevStep() {
  if (activeStep.value > 0) {
    activeStep.value--;
    window.scrollTo(0, 0);
  }
}

// Funciones para gestionar fábricas
function addFactory() {
  const newFactory: FactoryFormType = {
    factorName: "",
    factoryAddress: "",
    country_id: {
      country_id: 0,
      countryName: "",
      countryISO: "",
    },
    factoryProvince: "",
    factoryCity: "",
    factoryState: "",
    zipCode: "",
    factoryPhone: "",
    exportLicenceCode: "",
    mainProduct: "",
    finishProduct: false,
  };

  // Asegurarse de que country_id sea un objeto completo
  if (!newFactory.country_id) {
    newFactory.country_id = {
      country_id: 0,
      countryName: "",
      countryISO: "",
    };
  }

  formData.value.factories.push(newFactory);
  console.log("Nueva fábrica agregada:", newFactory);
}

function removeFactory(index: number) {
  if (index > 0) {
    formData.value.factories.splice(index, 1);
    // Eliminar errores de validación asociados
    delete factoryValidationErrors[index];
  }
}

// Funciones específicas para validar selects
function validateCountrySelect(): boolean {
  if (!formData.value.country_id || !formData.value.country_id.country_id) {
    validationErrors["country_id"] = "Country is required";
    console.log("Country validation failed", formData.value.country_id);
    return false;
  }
  return true;
}

function validateCurrencySelect(): boolean {
  if (!formData.value.currencyId || !formData.value.currencyId.currencyId) {
    validationErrors["currencyId"] = "Currency is required";
    console.log("Currency validation failed", formData.value.currencyId);
    return false;
  }
  return true;
}

function validateSupplierTypeSelect(): boolean {
  if (!formData.value.status_id || !formData.value.status_id.typeSupplierId) {
    validationErrors["status_id"] = "Supplier Type is required";
    console.log("Supplier Type validation failed", formData.value.status_id);
    return false;
  }
  return true;
}

function validateSupplierCategorySelect(): boolean {
  if (!formData.value.nivel3 || !formData.value.nivel3.id) {
    validationErrors["nivel3"] = "Supplier Category is required";
    console.log("Supplier Category validation failed", formData.value.nivel3);
    return false;
  }
  return true;
}

function validateFactoryCountrySelect(
  factory: FactoryFormType,
  index: number
): boolean {
  if (!factory.country_id || !factory.country_id.country_id) {
    if (!factoryValidationErrors[index]) {
      factoryValidationErrors[index] = {};
    }
    factoryValidationErrors[index]["country_id"] = "Country is required";
    console.log("Factory Country validation failed", factory.country_id);
    return false;
  }
  return true;
}

// Funciones de validación
function validateCurrentStep(): boolean {
  // Limpiar errores previos
  Object.keys(validationErrors).forEach((key) => delete validationErrors[key]);

  let isValid = true;

  // Validar según el paso actual
  if (activeStep.value === 0) {
    // Validar información de la empresa
    schema.companyInfo.forEach((field) => {
      // Validación específica para selects
      if (field.type === "select") {
        if (field.required) {
          if (field.key === "country_id") {
            if (!validateCountrySelect()) isValid = false;
          } else if (field.key === "currencyId") {
            if (!validateCurrencySelect()) isValid = false;
          } else if (field.key === "supplierType") {
            if (!validateSupplierTypeSelect()) isValid = false;
          } else if (field.key === "supplierLvl3") {
            if (!validateSupplierCategorySelect()) isValid = false;
          }
        }
      } else if (
        field.required &&
        !formData.value[field.key as keyof SupplierInformationForm]
      ) {
        validationErrors[field.key] = `${field.label} is required`;
        isValid = false;
      }
    });

    // Validaciones específicas
    if (formData.value.email && !validateEmail(formData.value.email)) {
      validationErrors["email"] = "Please enter a valid email address";
      isValid = false;
    }

    if (formData.value.phone && !validatePhone(formData.value.phone)) {
      validationErrors["phone"] = "Please enter a valid phone number";
      isValid = false;
    }

    if (
      formData.value.companyOwnerEmail &&
      !validateEmail(formData.value.companyOwnerEmail)
    ) {
      validationErrors["companyOwnerEmail"] =
        "Please enter a valid email address";
      isValid = false;
    }

    if (
      formData.value.companyOwnerPhone &&
      !validatePhone(formData.value.companyOwnerPhone)
    ) {
      validationErrors["companyOwnerPhone"] =
        "Please enter a valid phone number";
      isValid = false;
    }
  } else if (activeStep.value === 1) {
    // Validar información bancaria
    schema.bankInfo.forEach((field) => {
      if (
        field.required &&
        !formData.value[field.key as keyof SupplierInformationForm]
      ) {
        validationErrors[field.key] = `${field.label} is required`;
        isValid = false;
      }
    });

    // Validaciones específicas
    if (
      formData.value.swiftCode &&
      !validateSwiftCode(formData.value.swiftCode)
    ) {
      validationErrors["swiftCode"] = "Please enter a valid SWIFT code";
      isValid = false;
    }

    if (formData.value.ibanNumber && !validateIBAN(formData.value.ibanNumber)) {
      validationErrors["ibanNumber"] = "Please enter a valid IBAN number";
      isValid = false;
    }
  } else if (activeStep.value === 2) {
    // Validar información de fábricas
    if (formData.value.factories.length === 0) {
      toast.add({
        severity: "error",
        summary: "Error",
        detail: "At least one factory is required",
        life: 3000,
      });
      isValid = false;
    } else {
      // Limpiar errores previos de fábricas
      Object.keys(factoryValidationErrors).forEach(
        (index) => delete factoryValidationErrors[index]
      );

      formData.value.factories.forEach((factory, index) => {
        factorySchema.forEach((field) => {
          // Validación específica para selects
          if (field.type === "select") {
            if (field.required) {
              if (field.key === "country_id") {
                if (!validateFactoryCountrySelect(factory, index))
                  isValid = false;
              }
            }
          } else if (
            field.required &&
            !factory[field.key as keyof FactoryFormType]
          ) {
            if (!factoryValidationErrors[index]) {
              factoryValidationErrors[index] = {};
            }
            factoryValidationErrors[index][
              field.key
            ] = `${field.label} is required`;
            isValid = false;
          }
        });

        // Validaciones específicas para fábricas
        if (factory.factoryPhone && !validatePhone(factory.factoryPhone)) {
          if (!factoryValidationErrors[index]) {
            factoryValidationErrors[index] = {};
          }
          factoryValidationErrors[index]["factoryPhone"] =
            "Please enter a valid phone number";
          isValid = false;
        } 
      });   
    }
  } else if(activeStep.value === 3) {

    if (formData.value.certificates.length === 0) {
      toast.add({
        severity: "error",
        summary: "Error",
        detail: "Specify whether you have certificates",
        life: 3000,
      });
      isValid = false;
    }
  }

  if (!isValid && activeStep.value < 3) {
    toast.add({
      severity: "error",
      summary: "Validation Error",
      detail: "Please fill in all required fields correctly",
      life: 3000,
    });
  }

  return isValid;
}

function getFactoryValidationError(
  factoryIndex: number,
  fieldKey: string
): string | undefined {
  return factoryValidationErrors[factoryIndex]?.[fieldKey];
}

// Funciones auxiliares de validación
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validatePhone(phone: string): boolean {
  // Acepta formatos como: +1234567890, ************, (*************
  const phoneRegex =
    /^(\+?\d{1,3}[-\.\s]?)?\(?\d{3}\)?[-\.\s]?\d{3}[-\.\s]?\d{4}$/;
  return phoneRegex.test(phone);
}

function validateSwiftCode(swift: string): boolean {
  // Formato básico de código SWIFT: 8 o 11 caracteres alfanuméricos
  const swiftRegex = /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
  return swiftRegex.test(swift);
}

function validateIBAN(iban: string): boolean {
  // Formato básico IBAN: 2 letras seguidas de números
  const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/;
  return ibanRegex.test(iban);
}

// Función para obtener el nombre del país a partir del ID o del objeto
function getCountryName(countryData: any): string {
  // Si es un objeto con la propiedad countryName, usamos esa propiedad directamente
  if (
    typeof countryData === "object" &&
    countryData &&
    countryData.countryName
  ) {
    return countryData.countryName;
  }

  // Si es un número, buscamos el país por su ID
  if (typeof countryData === "number") {
    const country = countries.value.find((c) => c.country_id === countryData);
    return country ? country.countryName : "Unknown";
  }

  // Si es un objeto con la propiedad country_id, buscamos el país por su ID
  if (
    typeof countryData === "object" &&
    countryData &&
    countryData.country_id
  ) {
    const country = countries.value.find(
      (c) => c.country_id === countryData.country_id
    );
    return country ? country.countryName : "Unknown";
  }

  console.log("No se pudo determinar el país:", countryData);
  return "Unknown";
}

// Enviar formulario
async function submitForm() {
  if (validateCurrentStep()) {
    submitting.value = true;

    try {
      // Preparar datos para enviar al backend
      const formattedData = {
        ...formData.value,
        country_id: {
          country_id: formData.value.country_id.country_id,
        },
        currencyId: {
          currencyId: formData.value.currencyId.currencyId,
        },
        nivel3: {
          id: formData.value.nivel3?.id,
        },
        status_id: {
          status_id: formData.value.status_id.typeSupplierId,
        },
        factories: formData.value.factories.map((factory) => ({
          ...factory,
          country_id: {
            country_id: factory.country_id.country_id,
          },
          finishProduct: factory.finishProduct ? 1 : 0,
        })),
      };

      const response = await createSupplier(formattedData);
      console.log("Form submitted successfully", response);

      toast.add({
        severity: "success",
        summary: "Success",
        detail: "Company profile saved successfully",
        life: 3000,
      });

      // Redirigir al Home después de guardar
      setTimeout(() => {
        router.push("/");
      }, 2000);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.add({
        severity: "error",
        summary: "Error",
        detail: "An error occurred while saving your data",
        life: 3000,
      });
    } finally {
      submitting.value = false;
    }
  }
}

// Observadores para depurar los valores de los selects
watch(
  () => formData.value.country_id,
  (nuevoValor) => {
    console.log("🌍 País seleccionado:", nuevoValor);
  },
  { immediate: true }
);

watch(
  () => formData.value.currencyId,
  (nuevoValor) => {
    console.log("💰 Moneda seleccionada:", nuevoValor);
  },
  { immediate: true }
);

watch(
  () => formData.value.status_id,
  (nuevoValor) => {
    console.log("🏢 Tipo de proveedor seleccionado:", nuevoValor);
  },
  { immediate: true }
);

watch(
  () => formData.value.nivel3,
  (nuevoValor) => {
    console.log("📊 Categoría de proveedor seleccionada:", nuevoValor);
  },
  { immediate: true }
);

// Observador para las fábricas
watch(
  () => formData.value.factories,
  (nuevoValor) => {
    console.log("🏭 Fábricas actualizadas:", nuevoValor);
  },
  { deep: true }
);
</script>

<style scoped>
.company-profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.stepper-container {
  margin-bottom: 2rem;
}

.step-panel {
  animation: fadeIn 0.3s ease-in-out;
}

.step-title {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.step-description {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

.factory-card {
  background-color: var(--surface-100);
  border: 1px solid var(--surface-200);
  transition: all 0.3s ease;
}

.factory-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.factory-title {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.review-section {
  background-color: var(--surface-50);
  border-radius: 8px;
  padding: 1.5rem;
}

.review-section h3 {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--surface-200);
  padding-bottom: 0.5rem;
}

.review-item {
  margin-bottom: 0.5rem;
}

.factory-review {
  background-color: var(--surface-100);
  border: 1px solid var(--surface-200);
}

.factory-review h4 {
  color: var(--primary-color);
  font-size: 1.1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--surface-200);
  padding-bottom: 0.5rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-danger {
  color: var(--red-500);
}

.attachments-section {
  margin-bottom: 20px;
}

.column1 {
  display: flex;
  flex-direction: column;
}
.row1 {
  display: flex;
  flex-direction: row;
}



</style>
