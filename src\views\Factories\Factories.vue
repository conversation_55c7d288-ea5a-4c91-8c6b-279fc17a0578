<template>
  <div class="factories-container">
    <div class="card">
      <div><i class="pi pi-arrow-left" style="font-size: 2rem; cursor: pointer;" @click="goBack"></i></div>
      <h1 class="text-center mb-4">Factories Management</h1>
      <p class="text-center text-500 mb-5">Manage your manufacturing facilities</p>
      
      <div class="content-placeholder">
        <i class="pi pi-cog" style="font-size: 4rem; color: var(--primary-color-lighter);"></i>
        <h2>Factories Management</h2>
        <p>This section is under development. You will be able to manage your factories here soon.</p>
        <Button label="Go Back" icon="pi pi-arrow-left" @click="goBack" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.push('/');
};
</script>

<style scoped>
.factories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.content-placeholder h2 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.content-placeholder p {
  margin-bottom: 2rem;
  color: var(--text-color-secondary);
  max-width: 600px;
}
</style>
