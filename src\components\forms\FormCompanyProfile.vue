<template>
  <div
    class="form-company-profile form-company-profile-flex"
  >
    <ScrollPanel style="height: 1000px; max-width: 1350px; min-width: 310px">
      <div class="form-container">
        <div class="containerSuppliers containerStyle">
          <h3 class="title">Supplier's Information</h3>
          <div class="separateTitleForm">(*) Required</div>
          <Form class="justifyFormSupplier row1">
            <div class="column1">
              <label>Company Name *</label>
              <InputText
                maxlength="50"
                class="inputsize"
                v-model="formSupplier.companyName"
                name="companyName"
                type="text"
                placeholder="Company Name"
              />
              <div class="errorSize">
                <small v-if="errors.companyName" class="text-red-500">{{
                  errors.companyName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Company Owner Name *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.companyOwnerName"
                name="companyOwnerName"
                type="text"
                placeholder="Company Owner Name"
              />
              <div class="errorSize">
                <small v-if="errors.companyOwnerName" class="text-red-500">{{
                  errors.companyOwnerName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Company Owner Email *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.companyOwnerEmail"
                name="companyOwnerEmail"
                type="text"
                placeholder="Company Owner Email"
              />
              <div class="errorSize">
                <small v-if="errors.companyOwnerEmail" class="text-red-500">{{
                  errors.companyOwnerEmail
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Company Owner Phone *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.companyOwnerPhone"
                name="companyOwnerPhone"
                type="text"
                placeholder="Company Owner Phone"
              />
              <div class="errorSize">
                <small v-if="errors.companyOwnerPhone" class="text-red-500">{{
                  errors.companyOwnerPhone
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Social credit code/CIN *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.socialCreditCode"
                name="socialCreditCode"
                type="text"
                placeholder="Social Credit Code/CIN"
              />
              <div class="errorSize">
                <small v-if="errors.socialCreditCode" class="text-red-500">{{
                  errors.socialCreditCode
                }}</small>
              </div>
            </div>
            <div class="column1">
              <label>Export License Code *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.exportLicenseCode"
                name="exportLicenseCode"
                placeholder="Export License Code"
              />
              <div class="errorSize">
                <small v-if="errors.exportLicenseCode" class="text-red-500">
                  {{ errors.exportLicenseCode }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>Address *</label>
              <InputText
                maxlength="200"
                class="inputsize"
                v-model="formSupplier.address"
                name="address"
                type="text"
                placeholder="Address"
              />
              <div class="errorSize">
                <small v-if="errors.address" class="text-red-500">{{
                  errors.address
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>County/Province *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.province"
                name="province"
                placeholder="County/Province"
              />
              <div class="errorSize">
                <small v-if="errors.province" class="text-red-500">{{
                  errors.province
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>City *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.city"
                name="city"
                placeholder="City"
              />
              <div class="errorSize">
                <small v-if="errors.city" class="text-red-500">{{
                  errors.city
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>State *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.state"
                name="state"
                placeholder="State"
              />
              <div class="errorSize">
                <small v-if="errors.state" class="text-red-500">{{
                  errors.state
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Country *</label>
              <Dropdown
                v-model="selectedCountry"
                :options="countries"
                optionLabel="countryName"
                placeholder="Select a country"
                class="w-full md:w-80"
              />

              <div class="errorSize">
                <small v-if="errors.countryId" class="text-red-500">{{
                  errors.countryId
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Contact Name *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.contactName"
                name="contactName"
                type="text"
                placeholder="Contact Name"
              />
              <div class="errorSize">
                <small v-if="errors.contactName" class="text-red-500">{{
                  errors.contactName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Phone Number *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.phone"
                name="phone"
                placeholder="Phone Number"
              />
              <div class="errorSize">
                <small v-if="errors.phone" class="text-red-500">{{
                  errors.phone
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Email Address *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.email"
                name="email"
                placeholder="Email Address"
              />
              <div class="errorSize">
                <small v-if="errors.email" class="text-red-500">{{
                  errors.email
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Supplier Type</label>
              <Dropdown
                class="inputsize"
                name="supplierType"
                placeholder="Supplier Type"
              />
            </div>

            <div class="column1">
              <label>Payment Terms *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.paymentsTerms"
                name="paymentsTerms"
                placeholder="Payment Terms (e.g. Net 30)"
              />
              <div class="errorSize">
                <small v-if="errors.paymentsTerms" class="text-red-500">{{
                  errors.paymentsTerms
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Currency *</label>
              <Dropdown
                class="inputsize"
                name="currency"
                v-model="selectedCurrency"
                :options="currency"
                placeholder="Currency"
                optionLabel="currencyName"
              />
              <div class="errorSize">
                <small v-if="errors.currency" class="text-red-500">{{
                  errors.currency
                }}</small>
              </div>
            </div>
          </Form>
        </div>

        <div class="containerBank containerStyle">
          <h3>Bank Information</h3>
          <div class="separateTitleForm"></div>
          <Form class="justifyFormBank row1">
            <div class="column1">
              <label>Bank Name *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.bankName"
                name="bankName"
                placeholder="Bank Name"
              />
              <div class="errorSize">
                <small v-if="errors.bankName" class="text-red-500">{{
                  errors.bankName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Bank Address *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.bankAddress"
                name="bankAddress"
                placeholder="Bank Address"
              />
              <div class="errorSize">
                <small v-if="errors.bankAddress" class="text-red-500">{{
                  errors.bankAddress
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Swift Code *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.swiftCode"
                name="swiftCode"
                placeholder="Swift Code"
              />
              <div class="errorSize">
                <small v-if="errors.swiftCode" class="text-red-500">{{
                  errors.swiftCode
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>IBAN Number</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.ibanNumber"
                name="ibanNumber"
                placeholder="IBAN Number"
              />
              <div class="errorSize">
                <small v-if="errors.ibanNumber" class="text-red-500">{{
                  errors.ibanNumber
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Account Number *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.accountNumber"
                name="accountNumber"
                placeholder="Account Number"
              />
              <div class="errorSize">
                <small v-if="errors.accountNumber" class="text-red-500">{{
                  errors.accountNumber
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Beneficiary Name *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.beneficiaryName"
                name="beneficiaryName"
                placeholder="Beneficiary Name"
              />
              <div class="errorSize">
                <small v-if="errors.beneficiaryName" class="text-red-500">{{
                  errors.beneficiaryName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Beneficiary Address *</label>
              <InputText
                class="inputsize"
                v-model="formSupplier.beneficiaryAddress"
                name="beneficiaryAddress"
                placeholder="Beneficiary Address"
              />
              <div class="errorSize">
                <small v-if="errors.beneficiaryAddress" class="text-red-500">{{
                  errors.beneficiaryAddress
                }}</small>
              </div>
            </div>
          </Form>
        </div>

        <!-- FACTORY INFORMATION -->

        <div
          v-for="(factory, index) in factoryForms"
          :key="index"
          class="containerFactory containerStyle"
        >
          <h3>Factory Information #{{ index + 1 }}</h3>
          <div class="separateTitleForm"></div>
          <Form class="justifyFormFactory row1">
            <div class="column1">
              <label>Factory Name *</label>
              <InputText
                class="inputsize"
                v-model="factory.factorName"
                name="factorName"
                placeholder="Factory Name"
              />
              <div class="errorSize">
                <small v-if="errors.factoryName" class="text-red-500">{{
                  errors.factoryName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Factory Address *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryAddress"
                name="factoryAddress"
                placeholder="Factory Address"
              />
              <div class="errorSize">
                <small v-if="errors.factoryAddress" class="text-red-500">{{
                  errors.factoryAddress
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Country *</label>
              <Dropdown
                class="inputsize"
                name="countryName"
                v-model="selectedCountryFactory"
                :options="countries"
                placeholder="Country"
              />
              <div class="errorSize">
                <small v-if="errors.factoryCountry" class="text-red-500">{{
                  errors.factoryCountry
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>County/Province *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryProvince"
                name="factoryProvince"
                placeholder="County/Province"
              />
              <div class="errorSize">
                <small v-if="errors.factoryProvince" class="text-red-500">{{
                  errors.factoryProvince
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>City *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryCity"
                name="factoryCity"
                placeholder="City"
              />
              <div class="errorSize">
                <small v-if="errors.factoryCity" class="text-red-500">{{
                  errors.factoryCity
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>State *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryState"
                name="factoryState"
                placeholder="State"
              />
              <div class="errorSize">
                <small v-if="errors.factoryState" class="text-red-500">{{
                  errors.factoryState
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>ZIP Code *</label>
              <InputText
                class="inputsize"
                v-model="factory.zipCode"
                name="zipCode"
                placeholder="ZIP Code"
              />
              <div class="errorSize">
                <small v-if="errors.factoryZipCode" class="text-red-500">{{
                  errors.factoryZipCode
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Phone *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryPhone"
                name="factoryPhone"
                placeholder="Phone"
              />
              <div class="errorSize">
                <small v-if="errors.factoryPhone" class="text-red-500">{{
                  errors.factoryPhone
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Export License Code</label>
              <InputText
                class="inputsize"
                v-model="factory.exportLicenceCode"
                name="exportLicenceCode"
                placeholder="Export License Code (Optional)"
              />
              <div class="errorSize">
                <small v-if="errors.exportLicenseCode" class="text-red-500">
                  {{ errors.exportLicenseCode }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>Main Product </label>
              <InputText
                class="inputsize"
                v-model="factory.mainProduct"
                name="mainProduction"
                placeholder="Main Product (Optional)"
              />
            </div>

            <div class="column1">
              <label>Does this factory deliver the finished product?</label>
              <div class="row1">
                <div>
                  <RadioButton
                    v-model="factory.finishProduct"
                    inputId="Yes"
                    name="finishProduct"
                    value="yes"
                  />
                  <label for="Yes">Yes</label>
                </div>

                <div>
                  <RadioButton
                    v-model="factory.finishProduct"
                    inputId="No"
                    name="finishProduct"
                    value="No"
                  />
                  <label for="No">No</label>
                </div>
              </div>
            </div>

            <!-- Botón para eliminar esta fábrica (excepto la primera) -->
            <div
              v-if="index > 0"
              class="column1 flex justify-content-center align-items-center"
            >
              <Button
                icon="pi pi-trash"
                class="p-button-danger p-button-sm"
                @click="removeFactory(index)"
                label="Remove Factory"
              />
            </div>
          </Form>
        </div>
      </div>
    </ScrollPanel>
    <div class="controls">
      <div class="factory-controls column1">
        <div class="factory-count">
          <label>Total Factories: {{ factoryForms.length }}</label>
        </div>
        <Button
          id="btnFactory"
          @click="addFactory"
          class="btn"
          severity="secondary"
          label="Add More Factory Form"
        >
        </Button>
      </div>

      <div v-if="formSaved" class="success-message">
        <p>Information saved successfully!</p>
      </div>
      <div class="submit-container">
        <Button
          class="submitBtn"
          @click="handleUnifiedSubmit"
          severity="primary"
          label="Save All Information"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Form } from "@primevue/forms";
import { ref } from "vue";
import { DropdownClasses, ScrollPanel } from "primevue";
import { RadioButton } from "primevue";
import { onMounted } from "vue";
import { createSupplier } from "@/service/CompanyProfileService";
import type { SupplierInformationForm } from "@/interfaces/CompanyProfileInterface";
import { getCountries } from "@/service/SelecCountries";
import type { CountriesDB } from "@/interfaces/PaisesInterface";
import type { FactoryFormType } from "@/interfaces/FactoriesInteface";
import type { CurrencyDB } from "@/interfaces/CurrencyInterface";
import { getCurrency } from "@/service/SelectCurrency";

const currency = ref<CurrencyDB[]>([]);
const selectedCurrency = ref<CurrencyDB | null>(null);
const errorCurrency = ref<string | null>(null);

onMounted(async () => {
  try {
    currency.value = await getCurrency();
  } catch (err) {
    errorCurrency.value = "No se pudieron cargar los países.";
  }
});

// LÓGICA DROPDOWN PAISES
const countries = ref<CountriesDB[]>([]);
const selectedCountry = ref<CountriesDB | null>(null);
const errorCountries = ref<string | null>(null);

onMounted(async () => {
  try {
    countries.value = await getCountries();
  } catch (err) {
    errorCountries.value = "No se pudieron cargar los países.";
  }
});

// Flag to show success message
const formSaved = ref(false);

const errors = ref({
  companyName: "",
  companyOwnerName: "",
  companyOwnerPhone: "",
  companyOwnerEmail: "",
  socialCreditCode: "",
  exportLicenseCode: "",
  address: "",
  province: "",
  city: "",
  state: "",
  countryId: "",
  contactName: "",
  phone: "",
  email: "",
  paymentsTerms: "",
  currency: "",
  bankName: "",
  bankAddress: "",
  swiftCode: "",
  ibanNumber: "",
  accountNumber: "",
  beneficiaryName: "",
  beneficiaryAddress: "",
  factoryName: "",
  factoryAddress: "",
  factoryCountry: "",
  factoryProvince: "",
  factoryCity: "",
  factoryState: "",
  factoryZipCode: "",
  factoryPhone: "",
  mainProduct: "",
});

// FORMULARIO DE INFORMACIÓN DEL PROVEEDOR
const formSupplier = ref<SupplierInformationForm>({
  companyName: "",
  companyOwnerName: "",
  companyOwnerPhone: "",
  companyOwnerEmail: "",
  socialCreditCode: "",
  exportLicenseCode: "",
  address: "",
  province: "",
  city: "",
  state: "",
  CountriesDB: {
    countryId: 0,
    countryName: "",
    countryISO: "",
  },
  contactName: "",
  phone: "",
  email: "",
  paymentsTerms: "",
  currency: "",
  currencyId: 0,
  bankName: "",
  bankAddress: "",
  swiftCode: "",
  ibanNumber: "",
  accountNumber: "",
  beneficiaryName: "",
  beneficiaryAddress: "",
  companyProfileStatus: 1,
  factories: [
    {
      factorName: "",
      factoryAddress: "",
      CountriesDB: {
        countryId: 0,
        countryName: "",
        countryISO: "",
      },
      factoryProvince: "",
      factoryCity: "",
      factoryState: "",
      zipCode: "",
      factoryPhone: "",
      exportLicenceCode: "",
      mainProduct: "",
      finishProduct: "",
    },
  ],
});

// Función para crear un nuevo formulario de fábrica vacío
const createEmptyFactoryForm = (): FactoryFormType => {
  return {
    factorName: "",
    factoryAddress: "",
    CountriesDB: {
      countryId: 0,
      countryName: "",
      countryISO: "",
    },
    factoryProvince: "",
    factoryCity: "",
    factoryState: "",
    zipCode: "",
    factoryPhone: "",
    exportLicenceCode: "",
    mainProduct: "",
    finishProduct: "",
  };
};

// Array reactivo para almacenar todos los formularios de fábrica
const factoryForms = ref<FactoryFormType[]>([createEmptyFactoryForm()]);

// Función para agregar un nuevo formulario de fábrica
const addFactory = () => {
  factoryForms.value.push(createEmptyFactoryForm());
};

// Función para eliminar un formulario de fábrica
const removeFactory = (index: number) => {
  if (index > 0) {
    // No permitir eliminar la primera fábrica
    factoryForms.value.splice(index, 1);
  }
};

const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePhone = (phone: string): boolean => {
  // Acepta formatos como: +**********, ************, (*************
  const phoneRegex =
    /^(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/;
  return phoneRegex.test(phone);
};

const validateChineseSocialCreditCode = (code: string): boolean => {
  const regex = /^[1-9Y]{1}[1239]{1}[0-9]{6}[0-9A-Z]{9}[0-9X]$/;
  return regex.test(code);
};

const validateIndianCIN = (cin: string): boolean => {
  const regex = /^[UL][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/;
  return regex.test(cin);
};

const validateExportLicenseCode = (code: string): boolean => {
  // Suponemos que debe ser alfanumérico, entre 8 y 20 caracteres
  const regex = /^[A-Z0-9]{8,20}$/i;
  return regex.test(code);
};

const validateSwiftCode = (swift: string): boolean => {
  // Formato básico de código SWIFT: 8 o 11 caracteres alfanuméricos
  const swiftRegex = /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
  return swiftRegex.test(swift);
};

const validateIBAN = (iban: string): boolean => {
  // Formato básico IBAN: 2 letras seguidas de números
  const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/;
  return ibanRegex.test(iban);
};

const selectedCountryFactory = ref<CountriesDB | null>(null);
// Manejador unificado para enviar todos los formularios
const handleUnifiedSubmit = async () => {
  // Set countryId from selectedCountry if available
  if (selectedCountry.value) {
    formSupplier.value.countryId = {
      countryId: selectedCountry.value.countryId,
    };
  }

  // Set currency from selectedCurrency if available
  if (selectedCurrency.value) {
    formSupplier.value.currency = selectedCurrency.value.currencyName;
  }

  // Create a clean supplier object without reactive properties
  const cleanSupplier = {
    companyName: formSupplier.value.companyName,
    companyOwnerName: formSupplier.value.companyOwnerName,
    companyOwnerPhone: formSupplier.value.companyOwnerPhone,
    companyOwnerEmail: formSupplier.value.companyOwnerEmail,
    socialCreditCode: formSupplier.value.socialCreditCode,
    exportLicenseCode: formSupplier.value.exportLicenseCode,
    address: formSupplier.value.address,
    province: formSupplier.value.province,
    city: formSupplier.value.city,
    state: formSupplier.value.state,
    countryId: formSupplier.value.countryId,
    contactName: formSupplier.value.contactName,
    phone: formSupplier.value.phone,
    email: formSupplier.value.email,
    paymentsTerms: formSupplier.value.paymentsTerms,
    currency: formSupplier.value.currency,
    bankName: formSupplier.value.bankName,
    bankAddress: formSupplier.value.bankAddress,
    swiftCode: formSupplier.value.swiftCode,
    ibanNumber: formSupplier.value.ibanNumber,
    accountNumber: formSupplier.value.accountNumber,
    beneficiaryName: formSupplier.value.beneficiaryName,
    beneficiaryAddress: formSupplier.value.beneficiaryAddress,
    companyProfileStatus: formSupplier.value.companyProfileStatus,
  };

  // Create clean factory objects
  const cleanFactories = factoryForms.value.map((factory) => {
    // Set factoryCountry from selectedCountry if available
    let factoryCountryValue = selectedCountryFactory.value.countryId;
    console.log("aca estamos",factoryCountryValue);

    if (
      typeof factory.factoryCountry === "object" &&
      factory.factoryCountry !== null
    ) {
      factoryCountryValue = (factory.factoryCountry as CountriesDB).countryId;
    }

    return {
      factorName: factory.factorName,
      factoryAddress: factory.factoryAddress,
      factoryCountry: factoryCountryValue,
      factoryProvince: factory.factoryProvince,
      factoryCity: factory.factoryCity,
      factoryState: factory.factoryState,
      zipCode: factory.zipCode,
      factoryPhone: factory.factoryPhone,
      exportLicenceCode: factory.exportLicenceCode,
      mainProduct: factory.mainProduct,
      finishProduct: factory.finishProduct,
    };
  });

  // Format the data according to the required structure
  const formattedData = {
    ...cleanSupplier,
    factories: cleanFactories,
  };

  // Now this should work without circular references
  const jsonData = JSON.stringify(formattedData, null, 2);

  try {
    console.log("Combined data saved successfully:", jsonData);
    const respuesta = await createSupplier(formattedData);
    console.log("json enviado correctamente", respuesta.data);

    // Mostrar mensaje de éxito
    formSaved.value = true;
    setTimeout(() => {
      formSaved.value = false;
    }, 5000);
    console.log("Successful form validation");
  } catch (error) {
    console.error("Error saving data:", error);
  }
};
</script>

<style scoped>
/* .form-company-profile {
  background-image: url(src/assets/images/background-casaideas-05.png);
} */

.form-company-profile-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.logo {
  height: 100px;
}

.form-container {
  display: flex;
  flex-flow: column;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  gap: 5px;
  padding: 10px;
}

.row1 {
  display: flex;
  flex-flow: row wrap;
}

.column1 {
  display: flex;
  flex-direction: column;
}

.justify-form {
  width: 520px;
  justify-content: center;
  gap: 10px;
}
.justifyFormSupplier {
  max-width: 1340px;
  min-width: 300px;
  max-height: 1420px;
  min-height: 330px;
  justify-content: center;
  gap: 10px;
}

.justifyFormBank {
  max-width: 1340px;
  min-width: 300px;
  max-height: 730px;
  min-height: 250px;
  justify-content: center;
  gap: 10px;
}

.justifyFormFactory {
  max-width: 1340px;
  min-width: 300px;
  max-height: 1200px;
  min-height: 250px;
  justify-content: center;
  gap: 10px;
}

.containerSuppliers {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  max-height: 1420px;
  min-height: 330px;
}

.containerBank {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  max-height: 730px;
  min-height: 250px;
}

.containerFactory {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  max-height: 1200px;
  min-height: 250px;
  margin-bottom: 20px;
}

.containerStyle {
  background-color: rgba(128, 128, 128, 0.284);
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 10px;
}

.inputsize {
  width: 250px;
}

.textarea {
  background-color: rgba(255, 255, 255, 0.685);
  width: 450px;
  height: 100px;
  border-radius: 5px;
}

.textarea-factory {
  background-color: rgba(255, 255, 255, 0.685);
  width: 300px;
  height: 100px;
  border-radius: 5px;
}

.errorSize {
  height: 21px;
}

.factory-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 400px;
  margin: 20px 0;
}

.factory-count {
  font-weight: bold;
}

.success-message {
  background-color: #4caf50;
  color: white;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  text-align: center;
}

.submit-container {
  margin: 20px 0;
}

.submitBtn {
  width: 180px;
}

.controls {
  display: flex;
  flex-direction: row;
  align-items: end;
  padding: 10px;
  gap: 10px;
}

.separateTitleForm {
  height: 30px;
}
</style>
