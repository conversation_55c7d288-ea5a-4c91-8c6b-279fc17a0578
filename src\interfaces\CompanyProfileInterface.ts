import type { FactoryFormType } from "./FactoriesInteface"
import type { CountriesDB } from "./PaisesInterface"
import type { CurrencyDB } from "./CurrencyInterface"
import type { SupplierLvl3Interface } from "./SupplierLvl3Interface"
import type { StatusProveedor } from "./StatusProvedor"
import type { CertificatesInterface } from "./CertificatesInterface"

export interface SupplierInformationForm{
    companyName: string,
    companyOwnerName: string,
    companyOwnerPhone: string,
    companyOwnerEmail: string,
    socialCreditCode: string,
    exportLicenseCode: string,
    address: string,
    province: string,
    city: string,
    state: string,
    country_id: CountriesDB,
    contactName: string,
    status_id: StatusProveedor,
    nivel3: SupplierLvl3Interface,
    phone: string,
    email: string,
    currencyId: CurrencyDB,
    bankName: string,
    bankAddress: string,
    swiftCode: string,
    ibanNumber: string,
    accountNumber: string,
    beneficiaryName: string,
    beneficiaryAddress: string,
    companyProfileStatus: string,
    annualFacturation: number,
    factories: FactoryFormType[],
    certificates: CertificatesInterface[],
    [key: string ]: any
}