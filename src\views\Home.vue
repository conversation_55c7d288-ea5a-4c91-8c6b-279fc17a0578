<template>
  <div class="supplier-dashboard">
    <Toast position="top-right" />

    <div class="dashboard-header">
      <div class="company-logo">
        <img src="/src/assets/images/CASA_IDEAS_COLOR.png" alt="Company Logo" />
      </div>
      <div class="header-content">
        <h1>Supplier Portal</h1>
        <p>Manage your supplier information and activities</p>
      </div>
    </div>

    <div class="dashboard-content">
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <p>Loading your dashboard...</p>
      </div>

      <div v-else class="dashboard-cards">
        <!-- Company Profile Card -->
        <NavigationCard
          title="Company Profile"
          description="Manage your company information and contact details"
          route="/company-profile"
          icon="pi pi-building"
          :disabled="false"
        />

        <!-- Factories Card -->
        <NavigationCard
          title="Factories"
          description="Manage your manufacturing facilities and production capabilities"
          route="/factories"
          icon="pi pi-cog"
          :disabled="!hasActiveProfile"
        />

        <!-- Quotations Card -->
        <NavigationCard
          title="Quotations"
          description="View and respond to quotation requests from buyers"
          route="/quotations"
          icon="pi pi-dollar"
        />
      </div>

      <div v-if="!loading && !hasActiveProfile" class="profile-alert">
        <div class="p-message p-message-info">
          <div class="p-message-wrapper">
            <span class="p-message-icon pi pi-info-circle"></span>
            <div class="p-message-text">
              <span class="font-bold block mb-1">Complete Your Company Profile</span>
              <p class="m-0">Please complete your company profile to unlock all features of the supplier portal.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import NavigationCard from '@/components/dashboard/NavigationCard.vue';
import { getSupplierProfileStatus } from '@/service/SupplierStatusService';

const toast = useToast();
const loading = ref(true);
const hasActiveProfile = ref(false);

onMounted(async () => {
  try {
    // Simulate loading time
    setTimeout(async () => {
      const status = await getSupplierProfileStatus();
      hasActiveProfile.value = status.hasActiveProfile;
      loading.value = false;
    }, 1000);
  } catch (error) {
    console.error('Error loading supplier status:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load your profile status. Please try again later.',
      life: 5000
    });
    loading.value = false;
  }
});
</script>

<style scoped>
.supplier-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.dashboard-header {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
}

.company-logo {
  margin-right: 2rem;
}

.company-logo img {
  max-height: 80px;
  max-width: 200px;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color, #212529);
  font-size: 2rem;
}

.header-content p {
  margin: 0;
  color: var(--text-color-secondary, #6c757d);
  font-size: 1rem;
}

.dashboard-content {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 3rem 2rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loading-container p {
  margin-top: 1rem;
  color: var(--text-color-secondary, #6c757d);
}

.dashboard-cards {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.profile-alert {
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    text-align: center;
  }

  .company-logo {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .dashboard-cards {
    grid-template-columns: 1fr;
  }
}
</style>
