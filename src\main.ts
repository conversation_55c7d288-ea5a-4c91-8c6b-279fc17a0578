import { createApp } from 'vue';
import App from '@/App.vue';
import router from '@/router';
import '@/assets/style.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap';
import PrimeVue from 'primevue/config';
import Aura from '@primevue/themes/aura';
import InputText from "primevue/inputtext";
import Message from "primevue/message";
import Dropdown from 'primevue/dropdown';
import Button from 'primevue/button';
import Steps from 'primevue/steps';
import Card from 'primevue/card';
import Toast from 'primevue/toast';
import Divider from 'primevue/divider';
import InputMask from 'primevue/inputmask';
import Tooltip from 'primevue/tooltip';
import ToastService from 'primevue/toastservice';
import ScrollPanel from 'primevue/scrollpanel';
import RadioButton from 'primevue/radiobutton';
import ProgressBar from 'primevue/progressbar';
import ProgressSpinner from 'primevue/progressspinner';
import 'primeflex/primeflex.css';
import 'primeicons/primeicons.css';
import { FileUpload } from 'primevue';
import { createPinia } from "pinia";
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(ToastService);
app.component("InputText", InputText);
app.component("Message", Message);
app.component("Dropdown", Dropdown);
app.component("DataTable", DataTable);
app.component("Column", Column);
app.component("Button", Button);
app.component("Steps", Steps);
app.component("Card", Card);
app.component("Toast", Toast);
app.component("Divider", Divider);
app.component("InputMask", InputMask);
app.component("ScrollPanel", ScrollPanel);
app.component("RadioButton", RadioButton);
app.component("ProgressBar", ProgressBar);
app.component("ProgressSpinner", ProgressSpinner);
app.component("FileUpload", FileUpload)

// Directivas
app.directive('tooltip', Tooltip);

app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            darkModeSelector: false || 'none',
        }
    },
    ripple: true
});

app.mount('#app');
