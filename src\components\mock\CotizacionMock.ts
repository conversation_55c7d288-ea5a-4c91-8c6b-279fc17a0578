import type { Cotizacion } from '@/interfaces/CotizacionInterface';

export const cotizacionData: Cotizacion[] = [
  {
      quotationId: 1,
      statudId: 1,
      quotationDate: '2023-09-22',
      items: [{
        itemId: 1,
        sku: 1001,
        name: "Mouse Inalámbrico Logitech M185",
        productImage: "https://example.com/images/logitech-m185.jpg",
        productDescription: "Mouse inalámbrico con receptor USB, color gris.",
        supplierItemNumbre: "SUP-LOG-001"
      },
      {
        itemId: 2,
        sku: 1002,
        name: "Teclado Mecánico Redragon Kumara K552",
        productImage: "https://example.com/images/redragon-k552.jpg",
        productDescription: "Teclado mecánico retroiluminado con switches Outemu Blue.",
        supplierItemNumbre: "SUP-RED-002"
      }]
    },


  {
      quotationId: 1,
      statudId: 1,
      quotationDate: '2023-09-22',
    items: [{
      itemId: 3,
      sku: 1003,
      name: "Monitor LG UltraWide 29WL500",
      productImage: "https://example.com/images/lg-29wl500.jpg",
      productDescription: "Monitor 29 pulgadas UltraWide FHD IPS con HDMI.",
      supplierItemNumbre: "SUP-LG-003"
    }],
  }];