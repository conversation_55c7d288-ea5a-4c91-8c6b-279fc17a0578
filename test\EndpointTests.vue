<template>
  <div class="card p-fluid">
    <h1>Prueba de Servicio: Tipos de Plástico</h1>
    
    <div class="flex flex-column gap-3">
      <Button 
        label="Obtener Tipos de Plástico" 
        icon="pi pi-refresh" 
        @click="fetchPlasticTypes" 
        :loading="loading"
      />
      
      <Message v-if="error" severity="error">
        {{ error }}
        <pre v-if="errorDetails">{{ errorDetails }}</pre>
      </Message>
      
      <ProgressBar v-if="loading" mode="indeterminate" style="height: 6px"></ProgressBar>
      
      <DataTable 
        v-if="Array.isArray(plasticTypes) && plasticTypes.length > 0" 
        :value="plasticTypes" 
        stripedRows
        paginator 
        :rows="10"
        :rowsPerPageOptions="[5, 10, 20, 50]"
        tableStyle="min-width: 50rem"
      >
        <Column field="ptId" header="ID" sortable></Column>
        <Column field="ptPlastic" header="Tipo de Plástico" sortable></Column>
        <Column field="ptDescription" header="Descripción" sortable></Column>
      </DataTable>
      
      <Card v-else-if="!loading && !error">
        <template #content>
          <p class="text-center">No hay datos de tipos de plástico disponibles</p>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getPlasticType } from '@/service/PlasticTypeService';
import Button from 'primevue/button';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import ProgressBar from 'primevue/progressbar';
import Message from 'primevue/message';
import Card from 'primevue/card';

interface PlasticType {
  ptId: number;
  ptPlastic: string;
  ptDescription: string;
}

const plasticTypes = ref<PlasticType[] | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);
const errorDetails = ref<any>(null);

const fetchPlasticTypes = async () => {
  try {
    loading.value = true;
    error.value = null;
    errorDetails.value = null;
    plasticTypes.value = null;

    const data = await getPlasticType();
    console.log('Datos recibidos:', data);

    if (typeof data === 'string' && data.includes('<!doctype html>')) {
      throw new Error('El servidor devolvió HTML en lugar de JSON');
    }

    if (Array.isArray(data)) {
      plasticTypes.value = data;
    } else if (data && typeof data === 'object') {
      plasticTypes.value = [data];
    } else {
      throw new Error('Formato de datos inesperado');
    }
  } catch (err: any) {
    console.error('Error:', err);
    error.value = 'Error al cargar los tipos de plástico';
    errorDetails.value = err.response?.data || err.message;
  } finally {
    loading.value = false;
  }
};
</script>