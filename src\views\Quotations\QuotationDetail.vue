<template>
  <div v-if="cotizacionSeleccionada" class="quotations-container">
    <div class="card">
      <DataTable :value="[cotizacionSeleccionada]">
        <Column field="quotationId" header="ID"></Column>
        <Column field="statudId" header="Status"></Column>
        <Column field="quotationDate" header="Date"></Column>
        <Column header="Item id">
          <template #body="slotProps">
            <ul>
              <li v-for="(item, index) in slotProps.data.items" :key="index">
                {{ item.itemId }}
              </li>
            </ul>
          </template>
        </Column>
        <Column header="Name">
          <template #body="slotProps">
            <ul>
              <li v-for="(item, index) in slotProps.data.items" :key="index">
                {{ item.name }}
              </li>
            </ul>
          </template>
        </Column>
        <Column header="SKU'S">
          <template #body="slotProps">
            <ul>
              <li v-for="(item, index) in slotProps.data.items" :key="index">
                {{ item.sku }}
              </li>
            </ul>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
  <div v-else>
    <p>No hay cotización seleccionada</p>
  </div>
</template>


<script lang="ts" setup>
import { useCotizacionStore } from '@/stores/quotationStore';
import { storeToRefs } from 'pinia';

const cotizacionStore = useCotizacionStore();
const { cotizacionSeleccionada } = storeToRefs(cotizacionStore);

</script>

<style scoped>

.quotations-container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 1rem;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

</style>