<template>
  <div class="quotations-container">
    <div class="card">
      <div><i class="pi pi-arrow-left" style="font-size: 2rem; cursor: pointer;" @click="goBack"></i></div>
      <h1 class="text-center mb-4">Quotations</h1>
      <div class="content-placeholder">
        <DataTable :value="cotizacion" :paginator="true" :rows="10" :rowsPerPageOptions="[10, 25, 50]">
          <Column field="quotationId" header="ID"></Column>
          <Column field="statudId" header="Status"></Column>
          <Column field="quotationDate" header="Date"></Column>
          <Column header="Item id">
            <template #body="slotProps">
              <ul>
                <li v-for="(item, index) in slotProps.data.items" :key="index">
                  {{ item.itemId }}
                </li>
              </ul>
            </template>
          </Column>
          <Column header="Name">
            <template #body="slotProps">
              <ul>
                <li v-for="(item, index) in slotProps.data.items" :key="index">
                  {{ item.name }}
                </li>
              </ul>
            </template>
          </Column>
          <Column header="SKU'S">
            <template #body="slotProps">
              <ul>
                <li v-for="(item, index) in slotProps.data.items" :key="index">
                  {{ item.sku }}
                </li>
              </ul>
            </template>
          </Column>
          <Column header="Action">
            <template #body="slotProps">
              <Button 
                label="Fill Quotation" 
                icon="pi pi-eye" 
                @click="verDetalle(slotProps.data)" 
                class="p-button-sm"
              />
            </template>
          </Column>

        </DataTable>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Cotizacion } from '@/interfaces/CotizacionInterface';
import { ref } from 'vue';
import { DataTable, Column }  from 'primevue';
import { onMounted } from 'vue';
import { cotizacionData } from '@/components/mock/CotizacionMock';
import { useRouter } from 'vue-router';
import { useCotizacionStore } from '@/stores/quotationStore';




const cotizacionStore = useCotizacionStore();
const router = useRouter();
const cotizacion = ref<Cotizacion[]>([]);
const cotizacionValues = ref<Cotizacion[]>(cotizacionData);


const goBack = () => {
  router.push('/');
};

onMounted(() =>{
  cotizacion.value = [...cotizacionValues.value]
});


const verDetalle = (cotizacion: Cotizacion) => {
  cotizacionStore.setCotizacion(cotizacion);
  router.push('/SupplierQuotation');
  console.log('Cotización cargada:', cotizacion);
};

</script>

<style scoped>
.quotations-container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.content-placeholder h2 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.content-placeholder p {
  margin-bottom: 2rem;
  color: var(--text-color-secondary);
  max-width: 600px;
}
</style>
