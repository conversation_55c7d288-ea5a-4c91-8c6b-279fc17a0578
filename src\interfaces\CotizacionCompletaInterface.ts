import type { PlasticType } from '@/interfaces/PlasticTypeInterface';

export interface CotizacionCompletaInterface {
    materialPrice: number | null,
    accessoriesPrice: number | null,
    labourCost: number | null,
    oekotexCetificate: number | null,
    totalProductPrice: number | null,   
    // packagingSpecification: string,
    packagingPlastic: PlasticType | number,
    packagingFSC: number | null,
    packagingFobUsdPrice: number | null,
    // useSensorMatic: string,
    sensorMaticCost: number | null,
    supplierInnerQuantity: number | null,
    innerDescription: string,
    cbmInnerM3: number | null,
    supplierMasterQuantity: number | null,
    masterDescription: string,
    cbmMasterM3: number | null,
    masterWeightsKg: number | null,
    masterInnerFscCertificate: number | null,
    moq: number | null,
    moqConditionId: number | null,
    shipmentPort: string,
    finalFobPrice: number | null,
    finalFobPriceFsc: number | null,
    productionLeadTimeFirstOrder: number | null,
    productionLeadTimeRepeatOrder: number | null,
    remarksHandlingCharge: string,
    [key: string ]: any

}